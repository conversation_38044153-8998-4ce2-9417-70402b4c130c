{
    "cSpell.words": [
        "axebuilder",
        "clas",
        "CLI's",
        "dashedstroke",
        "datetime",
        "daygrid",
        "deduplication",
        "dispatchable",
        "dottedstroke",
        "Dropdown",
        "fullcalendar",
        "fullscreen",
        "highchart",
        "Highcharts",
        "instrumenter",
        "intacct",
        "isbinaryfile",
        "jsbarcode",
        "konva",
        "localizable",
        "nostroke",
        "palettization",
        "palettized",
        "palletisation",
        "rebounces",
        "recompiles",
        "sdata",
        "solidfill",
        "solidstroke",
        "timegrid",
        "transpiles",
        "Transpiling",
        "uncollapsed",
        "Validatable",
        "wdio",
        "xalpha",
        "xanchors",
        "xcaption",
        "xcolor",
        "xdraw",
        "xend",
        "xfill",
        "xfilled",
        "xgtype",
        "xprops",
        "xshadow",
        "xshape",
        "xstart",
        "xstroke",
        "xtext",
        "xthickness",
        "xtrem",
        "xtype"
    ],
    "csv-edit.readOption_hasHeader": "true",
    "csv-edit.writeOption_hasHeader": "true",
    "csv-edit.quoteAllFields": true,
    "cucumberautocomplete.steps": ["./platform/cli/xtrem-cli-atp/build/cucumber-steps.js"],
    "cucumberautocomplete.skipDocStringsFormat": true,
    "debug.javascript.unmapMissingSources": true,
    "debug.javascript.terminalOptions": {
        "skipFiles": [
            "**/node_modules/**", // include all the node modules stuff
            "!**/node_modules/@sage/**", // exclude sage node modules stuff
            "**/$KNOWN_TOOLS$/**" // include the common tools
        ]
    },
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.rulers": [120],
    // we need this to get eslint to work with xtrem plugin in xtrem-core
    // see https://github.com/microsoft/vscode-eslint/issues/696#issuecomment-545325711
    // and https://github.com/microsoft/vscode-eslint/issues/856#issuecomment-567952628
    "eslint.workingDirectories": [
        {
            "mode": "auto"
        }
    ],
    "eslint.options": {
        "reportUnusedDisableDirectives": "error"
    },

    "files.associations": {
        "*.graphql.hbs": "graphql",
        "*.json.hbs": "plainText",
        "**/pipelines/**/*.yml": "azure-pipelines"
    },
    "files.exclude": {
        "**/dist": true,
        "**/node_modules": true,
        "**/build": true,
        "**/instrumented": true,
        "**/out": true,
        "**/certificatetest": true,
        "**/.nyc_output": true
    },
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true,
    "files.watcherExclude": {
        "**/*": true
    },
    "git.ignoreLimitWarning": true,
    // we need this disable the 3-way merge editor and use the old more intuitive editor for merge diffs
    // see https://github.com/microsoft/vscode/issues/157361
    "git.mergeEditor": false,
    "git.branchProtection": ["master", "release/*"],
    "grammarly.files.include": ["**/*.md"],
    "js/ts.implicitProjectConfig.experimentalDecorators": true,
    "typescript.tsdk": "./node_modules/typescript/lib",
    "[typescript]": {
        "editor.insertSpaces": true,
        "editor.codeActionsOnSave": {
            "source.organizeImports": "explicit"
        }
    },
    "typescript.disableAutomaticTypeAcquisition": true,
    "typescript.preferences.importModuleSpecifier": "relative",
    "sqltools.connections": [
        {
            "previewLimit": 50,
            "server": "localhost",
            "port": 5432,
            "driver": "PostgreSQL",
            "name": "Postgres",
            "group": "Local",
            "database": "postgres",
            "username": "postgres",
            "password": "secret"
        }
    ],
    "yaml.schemas": {
        "./xtrem-config-json-schema.json": "xtrem-config.yml"
    },
    "files.trimTrailingWhitespace": true,
    "[feature]": {
        "editor.defaultFormatter": "alexkrechik.cucumberautocomplete"
    },
    "editor.codeActions.triggerOnFocusChange": true,
    "files.autoSave": "afterDelay",
    "editor.hover.delay": 1,
    "editor.hover.hidingDelay": 1,
    "editor.occurrencesHighlightDelay": 1,
    "editor.quickSuggestionsDelay": 1,
    "workbench.hover.delay": 1,
    "workbench.sash.hoverDelay": 1
}
